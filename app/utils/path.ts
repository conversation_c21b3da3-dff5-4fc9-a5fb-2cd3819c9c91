import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'

export const ROOT_PATH = resolve(dirname(fileURLToPath(import.meta.url)), '..', '..')

export const rootPath = (...paths: string[]) => resolve(ROOT_PATH, ...paths)

export const appPath = (...paths: string[]) => rootPath('app', ...paths)

export const storagePath = (...paths: string[]) => rootPath('storage', ...paths)

export const logsPath = (...paths: string[]) => storagePath('logs', ...paths)

export const workersPath = (...paths: string[]) => appPath('workers', ...paths)
