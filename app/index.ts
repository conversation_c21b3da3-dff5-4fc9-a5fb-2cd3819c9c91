import 'reflect-metadata'
import { tap } from '@kdt310722/utils/function'
import { initializeDatabase } from './core/database'
import { logger } from './core/logger'
import { startServer } from './core/server'
import { WorkerManager } from './workers/manager'

async function main() {
    const timer = tap(logger.createTimer(), () => logger.info('Starting application...'))

    await initializeDatabase()
    await startServer()
    await new WorkerManager().start()

    logger.stopTimer(timer, 'info', 'Application started!')
}

main().catch((error) => {
    logger.forceExit(1, 'fatal', 'Failed to start application', error)
})
